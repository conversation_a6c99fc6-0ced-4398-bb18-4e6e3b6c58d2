<template>
  <el-button @click="onClick"> 点击触发逻辑控制 </el-button>
</template>

<script>
import { vueMixin } from "sdata-plugin-adapter";
import eventActionDefinitions from "./eventActionDefinitions.js";

export default {
  name: "Child",
  props: {
    vueProps: Object,
  },
  mixins: [vueMixin()],
  data() {
    return {
      data: this.vueProps.data,
      eventActionDefinitions,
      actions: {
        message: this.message,
      }
    };
  },

  mounted() {},

  methods: {
    message(){

    },
    onClick() {
      // console.log('data--',this.vueProps);
      
      this.triggerEvent("onClick", {
        value: 1,
      });
    },
  },
};
</script>

<style></style>
